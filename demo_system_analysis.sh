#!/bin/bash

echo "=== Auggie CLI Demo: System Analysis Automation ==="
echo "This demo shows how Auggie can analyze system information and provide insights"
echo

# Demo 1: System resource analysis
echo "📊 Analyzing system resources..."
df -h | auggie --print "Analyze this disk usage output. Identify any potential storage issues and recommend actions if disk usage is high."

echo
echo "🔍 Checking memory usage..."
free -h | auggie --print "Analyze this memory usage. Is the system healthy? Any recommendations?"

echo
echo "⚡ Analyzing running processes..."
ps aux --sort=-%cpu | head -10 | auggie --print "Analyze the top CPU-consuming processes. Are there any concerning patterns or processes that might need attention?"

echo
echo "🌐 Network connection analysis..."
netstat -tuln | auggie --print "Analyze these network connections. Summarize what services are running and if there are any security considerations."

echo
echo "✅ System analysis complete!"
