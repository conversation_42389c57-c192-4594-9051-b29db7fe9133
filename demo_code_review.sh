#!/bin/bash

echo "=== Auggie CLI Demo: Automated Code Review ==="
echo "This demo shows how Auggie can perform code analysis and reviews"
echo

# Create sample code files for demo
echo "📝 Creating sample code files..."
mkdir -p sample_project

cat > sample_project/user_service.py << 'EOF'
import hashlib
import sqlite3

class UserService:
    def __init__(self):
        self.db = sqlite3.connect('users.db')
        
    def create_user(self, username, password):
        # TODO: Add input validation
        hashed_password = hashlib.md5(password.encode()).hexdigest()
        cursor = self.db.cursor()
        query = f"INSERT INTO users (username, password) VALUES ('{username}', '{hashed_password}')"
        cursor.execute(query)
        self.db.commit()
        return True
        
    def authenticate(self, username, password):
        hashed_password = hashlib.md5(password.encode()).hexdigest()
        cursor = self.db.cursor()
        query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{hashed_password}'"
        result = cursor.execute(query).fetchone()
        return result is not None
        
    def get_user_data(self, user_id):
        cursor = self.db.cursor()
        query = f"SELECT * FROM users WHERE id = {user_id}"
        return cursor.execute(query).fetchone()
EOF

cat > sample_project/api_handler.js << 'EOF'
const express = require('express');
const app = express();

app.get('/api/user/:id', (req, res) => {
    const userId = req.params.id;
    
    // Direct database query without validation
    const query = `SELECT * FROM users WHERE id = ${userId}`;
    
    db.query(query, (err, results) => {
        if (err) {
            console.log(err);
            res.status(500).send('Error');
        } else {
            res.json(results);
        }
    });
});

app.post('/api/upload', (req, res) => {
    const file = req.files.upload;
    const filename = req.body.filename || file.name;
    
    // Save file without validation
    file.mv(`./uploads/${filename}`, (err) => {
        if (err) {
            res.status(500).send(err);
        } else {
            res.send('File uploaded');
        }
    });
});

app.listen(3000);
EOF

echo
echo "🔍 Reviewing Python code for security issues..."
cat sample_project/user_service.py | auggie --print "Review this Python code for security vulnerabilities, best practices violations, and potential bugs. Provide specific recommendations."

echo
echo "🔍 Reviewing JavaScript code for vulnerabilities..."
cat sample_project/api_handler.js | auggie --print "Review this Node.js/Express code for security vulnerabilities, potential SQL injection, and other issues. Suggest improvements."

echo
echo "📊 Generating code quality report..."
echo "Code Review Report" > code_review_report.md
echo "==================" >> code_review_report.md
echo "" >> code_review_report.md
echo "## Python Code Analysis" >> code_review_report.md
cat sample_project/user_service.py | auggie --print --quiet "Provide a markdown-formatted security analysis of this Python code with bullet points of issues found" >> code_review_report.md
echo "" >> code_review_report.md
echo "## JavaScript Code Analysis" >> code_review_report.md
cat sample_project/api_handler.js | auggie --print --quiet "Provide a markdown-formatted security analysis of this JavaScript code with bullet points of issues found" >> code_review_report.md

echo "📄 Code review report generated: code_review_report.md"
echo "Preview:"
head -20 code_review_report.md

# Cleanup
rm -rf sample_project

echo
echo "✅ Code review demo complete!"
