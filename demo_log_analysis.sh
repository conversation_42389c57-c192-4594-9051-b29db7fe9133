#!/bin/bash

echo "=== Auggie CLI Demo: Log Analysis Automation ==="
echo "This demo shows how Auggie can analyze log files and extract insights"
echo

# Create sample log files for demo
echo "📝 Creating sample log files..."
cat > sample_app.log << 'EOF'
2024-01-15 10:30:15 INFO  Application started successfully
2024-01-15 10:30:16 INFO  Database connection established
2024-01-15 10:32:45 WARN  High memory usage detected: 85%
2024-01-15 10:35:12 ERROR Failed to connect to external API: timeout after 30s
2024-01-15 10:35:13 INFO  Retrying API connection...
2024-01-15 10:35:45 INFO  API connection restored
2024-01-15 10:40:22 ERROR Database query failed: connection lost
2024-01-15 10:40:23 WARN  Attempting database reconnection
2024-01-15 10:40:25 INFO  Database reconnected successfully
2024-01-15 10:45:30 ERROR Critical: Out of memory exception
2024-01-15 10:45:31 FATAL Application crashed due to memory exhaustion
EOF

cat > sample_access.log << 'EOF'
192.168.1.100 - - [15/Jan/2024:10:30:15 +0000] "GET /api/users HTTP/1.1" 200 1234
192.168.1.101 - - [15/Jan/2024:10:30:16 +0000] "POST /api/login HTTP/1.1" 200 567
10.0.0.50 - - [15/Jan/2024:10:30:17 +0000] "GET /admin HTTP/1.1" 403 89
192.168.1.102 - - [15/Jan/2024:10:30:18 +0000] "GET /api/data HTTP/1.1" 500 234
10.0.0.50 - - [15/Jan/2024:10:30:19 +0000] "GET /admin HTTP/1.1" 403 89
10.0.0.50 - - [15/Jan/2024:10:30:20 +0000] "GET /admin HTTP/1.1" 403 89
192.168.1.103 - - [15/Jan/2024:10:30:21 +0000] "GET /api/users HTTP/1.1" 200 1456
10.0.0.50 - - [15/Jan/2024:10:30:22 +0000] "POST /login HTTP/1.1" 401 45
EOF

echo
echo "🔍 Analyzing application logs for errors and patterns..."
cat sample_app.log | auggie --print "Analyze this application log. Identify critical issues, patterns, and provide recommendations for fixing problems."

echo
echo "🌐 Analyzing web access logs for security issues..."
cat sample_access.log | auggie --print "Analyze this web access log. Look for suspicious activity, failed authentication attempts, and security concerns. Provide a summary."

echo
echo "📈 Generating log summary report..."
echo "Application Log Summary:" > log_report.txt
echo "======================" >> log_report.txt
cat sample_app.log | auggie --print --quiet "Create a concise executive summary of the key issues found in this log file" >> log_report.txt
echo "" >> log_report.txt
echo "Access Log Summary:" >> log_report.txt
echo "==================" >> log_report.txt
cat sample_access.log | auggie --print --quiet "Create a concise security summary of this access log" >> log_report.txt

echo "📄 Report generated: log_report.txt"
cat log_report.txt

# Cleanup
rm -f sample_app.log sample_access.log

echo
echo "✅ Log analysis demo complete!"
