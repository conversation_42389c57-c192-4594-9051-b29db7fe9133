# Multi-stage Dockerfile based on ubuntu:latest
# Includes AWS CLI, kubectl, networking tools, and auggie CLI

FROM ubuntu:latest

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_MAJOR=22

# Update package list and install basic dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    gnupg \
    lsb-release \
    ca-certificates \
    software-properties-common \
    apt-transport-https \
    && rm -rf /var/lib/apt/lists/*

# Install networking tools
RUN apt-get update && apt-get install -y \
    net-tools \
    iputils-ping \
    traceroute \
    nmap \
    netcat-openbsd \
    dnsutils \
    tcpdump \
    iptables \
    iproute2 \
    telnet \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf awscliv2.zip aws/

# Install kubectl
RUN curl -fsSL https://pkgs.k8s.io/core:/stable:/v1.31/deb/Release.key | gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg \
    && echo 'deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/v1.31/deb/ /' | tee /etc/apt/sources.list.d/kubernetes.list \
    && apt-get update \
    && apt-get install -y kubectl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 22 (required for auggie CLI)
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_MAJOR}.x | bash - \
    && apt-get install -y nodejs

# Install auggie CLI globally
RUN npm install -g @augmentcode/auggie

# Install additional useful tools
RUN apt-get update && apt-get install -y \
    git \
    vim \
    nano \
    htop \
    jq \
    tree \
    zip \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN useradd -m -s /bin/bash devuser \
    && usermod -aG sudo devuser

# Set working directory
WORKDIR /workspace

# Change ownership of workspace to devuser
RUN chown -R devuser:devuser /workspace

# Switch to non-root user
USER devuser

# Set default shell
SHELL ["/bin/bash", "-c"]

# Verify installations
RUN aws --version \
    && kubectl version --client \
    && node --version \
    && npm --version \
    && auggie --version || echo "auggie installed (version check may require login)"

# Default command
CMD ["/bin/bash"]

# Labels for documentation
LABEL maintainer="DevOps Team"
LABEL description="Ubuntu-based container with AWS CLI, kubectl, networking tools, and auggie CLI"
LABEL version="1.0"
