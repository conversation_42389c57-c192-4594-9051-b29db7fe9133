#!/bin/bash

echo "=========================================="
echo "🤖 AUGGIE CLI AUTOMATION DEMO SUITE"
echo "=========================================="
echo "Demonstrating AI-powered automation capabilities"
echo

# Make all demo scripts executable
chmod +x demo_*.sh

echo "Available demos:"
echo "1. System Analysis - Analyze system resources and health"
echo "2. Log Analysis - Parse and analyze log files for issues"
echo "3. Code Review - Automated security and quality review"
echo "4. DevOps Automation - Infrastructure and deployment tasks"
echo "5. Data Analysis - Parse and analyze data files"
echo "6. Run All Demos"
echo

read -p "Select demo (1-6): " choice

case $choice in
    1)
        echo "🚀 Running System Analysis Demo..."
        ./demo_system_analysis.sh
        ;;
    2)
        echo "🚀 Running Log Analysis Demo..."
        ./demo_log_analysis.sh
        ;;
    3)
        echo "🚀 Running Code Review Demo..."
        ./demo_code_review.sh
        ;;
    4)
        echo "🚀 Running DevOps Automation Demo..."
        ./demo_devops_automation.sh
        ;;
    5)
        echo "🚀 Running Data Analysis Demo..."
        ./demo_data_analysis.sh
        ;;
    6)
        echo "🚀 Running All Demos..."
        echo
        ./demo_system_analysis.sh
        echo
        echo "=========================================="
        ./demo_log_analysis.sh
        echo
        echo "=========================================="
        ./demo_code_review.sh
        echo
        echo "=========================================="
        ./demo_devops_automation.sh
        echo
        echo "=========================================="
        ./demo_data_analysis.sh
        ;;
    *)
        echo "Invalid selection. Please run again and choose 1-6."
        exit 1
        ;;
esac

echo
echo "=========================================="
echo "✅ Demo complete!"
echo "Key Auggie CLI automation benefits shown:"
echo "• AI-powered analysis of system outputs"
echo "• Automated report generation"
echo "• Security and code quality reviews"
echo "• Infrastructure monitoring insights"
echo "• Data processing and summarization"
echo "• Perfect for CI/CD pipelines and cron jobs"
echo "=========================================="
