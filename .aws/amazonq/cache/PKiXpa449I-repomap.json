{"filePath":"c:\\Users\\<USER>\\development\\npoc\\next.config.mjs","importedFiles":[],"classes":[],"functions":[],"interfaces":[],"variables":[]}
{"filePath":"c:\\Users\\<USER>\\development\\npoc\\postcss.config.mjs","importedFiles":[],"classes":[],"functions":[],"interfaces":[],"variables":[]}
{"filePath":"c:\\Users\\<USER>\\development\\npoc\\app\\page.tsx","importedFiles":[],"classes":[],"functions":[],"interfaces":[],"variables":[]}
{"filePath":"c:\\Users\\<USER>\\development\\npoc\\app\\layout.tsx","importedFiles":[],"classes":[],"functions":[{"signature":"export function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>)","span":{"start":{"line":20,"column":15},"end":{"line":34,"column":1}}}],"interfaces":[],"variables":[{"signature":"export const metadata: Metadata = {\n  title: \"Create Next App\",\n  description: \"Generated by create next app\",\n};","span":{"start":{"line":15,"column":7},"end":{"line":18,"column":2}}}]}
{"filePath":"c:\\Users\\<USER>\\development\\npoc\\tailwind.config.ts","importedFiles":[],"classes":[],"functions":[],"interfaces":[],"variables":[]}
