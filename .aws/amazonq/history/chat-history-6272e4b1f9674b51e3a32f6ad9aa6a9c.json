{"filename": "chat-history-6272e4b1f9674b51e3a32f6ad9aa6a9c.json", "collections": [{"name": "tabs", "data": [], "idIndex": null, "binaryIndices": {"updatedAt": {"name": "updatedAt", "dirty": false, "values": []}, "isOpen": {"name": "isOpen", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": ["historyId"], "transforms": {}, "objType": "tabs", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": [], "isIncremental": false}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 1000, "autosaveHandle": null, "throttledSaves": true, "options": {"adapter": null, "autosave": true, "autoload": true, "autosaveInterval": 1000, "persistenceMethod": "fs", "serializationMethod": "normal", "destructureDelimiter": "$<\n", "recursiveWait": true, "recursiveWaitLimit": false, "recursiveWaitLimitDuration": 2000, "started": 1746060592504}, "persistenceMethod": "adapter", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS", "isIncremental": false}