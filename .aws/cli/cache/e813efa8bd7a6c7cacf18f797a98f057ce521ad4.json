{"Credentials": {"AccessKeyId": "ASIARLX6PVO7YRI3JO5U", "SecretAccessKey": "IToPrdFuznDQICibw+SnOgXfVwRcr99Rnts4RBgo", "SessionToken": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Expiration": "2025-08-19T20:10:03+00:00", "AccountId": "************"}, "AssumedRoleUser": {"AssumedRoleId": "AROARLX6PVO7XBVHMJVXR:botocore-session-**********", "Arn": "arn:aws:sts::************:assumed-role/OrganizationAccountAccessRole/botocore-session-**********"}, "ResponseMetadata": {"RequestId": "a00fdc93-79fe-453e-a7cc-1dec9c8c4e63", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "a00fdc93-79fe-453e-a7cc-1dec9c8c4e63", "x-amz-sts-extended-request-id": "************************************************", "content-type": "text/xml", "content-length": "1514", "date": "<PERSON><PERSON>, 19 Aug 2025 19:10:03 GMT"}, "RetryAttempts": 0}}