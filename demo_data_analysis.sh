#!/bin/bash

echo "=== Auggie CLI Demo: Data Analysis Automation ==="
echo "This demo shows how Auggie can analyze data files and generate insights"
echo

# Create sample data files
echo "📊 Creating sample data files..."
cat > sales_data.csv << 'EOF'
Date,Product,Category,Sales,Region
2024-01-01,Laptop,Electronics,1200,North
2024-01-01,Phone,Electronics,800,South
2024-01-01,Desk,Furniture,300,East
2024-01-02,Laptop,Electronics,1500,North
2024-01-02,Chair,Furniture,150,West
2024-01-02,Phone,Electronics,900,South
2024-01-03,Tablet,Electronics,600,East
2024-01-03,Desk,Furniture,350,North
2024-01-03,Monitor,Electronics,400,West
2024-01-04,Laptop,Electronics,1100,South
2024-01-04,Chair,Furniture,200,East
2024-01-04,Phone,Electronics,750,North
EOF

cat > server_metrics.json << 'EOF'
{
  "timestamp": "2024-01-15T10:30:00Z",
  "servers": [
    {
      "name": "web-01",
      "cpu_usage": 75.2,
      "memory_usage": 68.5,
      "disk_usage": 45.3,
      "network_in": 1024,
      "network_out": 2048,
      "status": "healthy"
    },
    {
      "name": "web-02",
      "cpu_usage": 89.7,
      "memory_usage": 92.1,
      "disk_usage": 78.9,
      "network_in": 2048,
      "network_out": 4096,
      "status": "warning"
    },
    {
      "name": "db-01",
      "cpu_usage": 45.3,
      "memory_usage": 87.6,
      "disk_usage": 89.2,
      "network_in": 512,
      "network_out": 1024,
      "status": "critical"
    }
  ]
}
EOF

echo
echo "💰 Analyzing sales data..."
cat sales_data.csv | auggie --print "Analyze this sales data CSV. Identify trends, top-performing products, regional performance, and provide business insights."

echo
echo "🖥️ Analyzing server metrics..."
cat server_metrics.json | auggie --print "Analyze these server metrics. Identify performance issues, servers that need attention, and provide recommendations for optimization."

echo
echo "📈 Generating executive summary..."
{
    echo "# Data Analysis Executive Summary"
    echo "Generated: $(date)"
    echo ""
    echo "## Sales Performance"
    cat sales_data.csv | auggie --print --quiet "Create a brief executive summary of sales performance from this CSV data"
    echo ""
    echo "## Infrastructure Health"
    cat server_metrics.json | auggie --print --quiet "Create a brief executive summary of server health from this JSON metrics data"
} > executive_summary.md

echo "📄 Executive summary generated: executive_summary.md"
cat executive_summary.md

echo
echo "🔍 Performing data validation..."
echo "Checking for data quality issues..."
cat sales_data.csv | auggie --print "Check this CSV data for any quality issues, missing values, inconsistencies, or anomalies that might affect analysis."

# Cleanup
rm -f sales_data.csv server_metrics.json

echo
echo "✅ Data analysis demo complete!"
