#!/bin/bash

echo "=== Auggie CLI Demo: DevOps Automation ==="
echo "This demo shows how <PERSON><PERSON> can assist with DevOps tasks and infrastructure analysis"
echo

# Demo 1: Docker container analysis
echo "🐳 Analyzing Docker environment..."
if command -v docker &> /dev/null; then
    docker ps -a | auggie --print "Analyze these Docker containers. Are there any stopped containers that should be cleaned up? Any resource concerns?"
    echo
    docker images | auggie --print "Analyze these Docker images. Are there any dangling images or optimization opportunities?"
else
    echo "Docker not available, simulating with sample data..."
    echo "CONTAINER ID   IMAGE          COMMAND                  CREATED       STATUS                    PORTS     NAMES
abc123def456   nginx:latest   \"/docker-entrypoint.…\"   2 hours ago   Up 2 hours                80/tcp    web-server
def456ghi789   mysql:5.7      \"docker-entrypoint.s…\"   3 hours ago   Exited (0) 30 minutes ago           old-database
ghi789jkl012   redis:alpine   \"docker-entrypoint.s…\"   1 day ago     Up 1 day                  6379/tcp  cache-server" | auggie --print "Analyze these Docker containers. Are there any stopped containers that should be cleaned up? Any resource concerns?"
fi

echo
echo "📦 Checking system packages for updates..."
if command -v apt &> /dev/null; then
    apt list --upgradable 2>/dev/null | head -10 | auggie --print "Analyze these available package updates. Are there any critical security updates that should be prioritized?"
elif command -v yum &> /dev/null; then
    yum check-update 2>/dev/null | head -10 | auggie --print "Analyze these available package updates. Are there any critical security updates that should be prioritized?"
else
    echo "Package manager not available, simulating..."
    echo "libssl1.1/focal-updates 1.1.1f-1ubuntu2.20 amd64 [upgradable from: 1.1.1f-1ubuntu2.19]
curl/focal-updates 7.68.0-1ubuntu2.18 amd64 [upgradable from: 7.68.0-1ubuntu2.17]
openssh-client/focal-updates 1:8.2p1-4ubuntu0.9 amd64 [upgradable from: 1:8.2p1-4ubuntu0.8]" | auggie --print "Analyze these available package updates. Are there any critical security updates that should be prioritized?"
fi

echo
echo "🔧 Analyzing system services..."
systemctl list-units --failed 2>/dev/null | auggie --print "Analyze any failed system services. What might be causing these failures and how should they be addressed?"

echo
echo "💾 Checking backup status simulation..."
echo "Backup Status Report:
Last full backup: 2024-01-14 02:00:00 - SUCCESS (2.3GB)
Last incremental: 2024-01-15 02:00:00 - FAILED (Connection timeout)
Database backup: 2024-01-15 01:00:00 - SUCCESS (450MB)
Config backup: 2024-01-13 12:00:00 - SUCCESS (12MB)" | auggie --print "Analyze this backup status report. What issues need attention and what actions should be taken?"

echo
echo "📊 Generating DevOps health report..."
echo "DevOps Health Report" > devops_report.txt
echo "====================" >> devops_report.txt
echo "Generated: $(date)" >> devops_report.txt
echo "" >> devops_report.txt

# Get system uptime and load
uptime | auggie --print --quiet "Summarize this system uptime and load information in 1-2 sentences" >> devops_report.txt
echo "" >> devops_report.txt

# Check disk space
df -h / | auggie --print --quiet "Summarize the root filesystem disk usage in 1 sentence" >> devops_report.txt

echo "📄 DevOps report generated: devops_report.txt"
cat devops_report.txt

echo
echo "✅ DevOps automation demo complete!"
